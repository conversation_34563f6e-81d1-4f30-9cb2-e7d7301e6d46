import { router, buildProcedure } from "../trpc";
import { getRateLimit } from "@/utils/redis/rateLimits";
import {
  unifiedSearchSchema,
  unifiedSearchResponseSchema,
} from "@/types/models/search";
import { TRPCError } from "@trpc/server";
import { executeUnifiedSearch } from "@/lib/search-service";

export const searchRouter = router({
  searchTasks: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 20,
      overSeconds: 60,
      prefix: "search:unified-tasks",
    }),
  })
    .input(unifiedSearchSchema)
    .output(unifiedSearchResponseSchema)
    .query(async ({ input }) => {
      try {
        return await executeUnifiedSearch(input);
      } catch (error) {
        console.error("Unified search error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Search failed. Please try again.",
          cause: error,
        });
      }
    }),
});
