import { z } from "zod";
import { streamText, convertToCoreMessages } from "ai";
import { openai } from "@ai-sdk/openai";
import { router } from "../trpc";
import { buildProcedure } from "../trpc";
import { createRateLimitMiddleware } from "../trpc";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/utils/redis/client";

// Create a rate limiter for chat requests (5 requests per minute)
const chatRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(5, "1 m"),
  analytics: true,
});

export const chatRouter = router({
  sendMessage: buildProcedure({
    type: "public",
    rateLimit: chatRateLimiter,
  })
    .input(
      z.object({
        messages: z.array(
          z.object({
            id: z.string(),
            role: z.enum(["user", "assistant"]),
            content: z.string(),
            timestamp: z.string().optional(),
            version: z.string().optional(),
          })
        ),
      })
    )
    .query(async function* ({ input }) {
      // Convert UI messages to model messages
      const modelMessages = convertToCoreMessages(input.messages);

      // Stream the AI response
      const result = streamText({
        model: openai("gpt-3.5-turbo"), // Using cheaper model for now
        system:
          "You are a helpful AI assistant. Keep responses concise and helpful.",
        messages: modelMessages,
      });

      // Yield each chunk as it comes in
      for await (const chunk of result.textStream) {
        yield chunk;
      }
    }),
});
