import { AgentBuilderResponse, AgentSummary } from "./agent-builder-response";

export type ChatMessage = {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: string;
  version?: string;
  // New field for structured responses
  structuredResponse?: AgentBuilderResponse;
};

export type AgentVersion = {
  id: string;
  version: string;
  createdAt: string;
  agentSummary?: AgentSummary;
};

export type AgentConfiguration = {
  id: string;
  name: string;
  description: string;
  currentVersion: string;
  versions: AgentVersion[];
  inputs: AgentInput[];
  outputs: AgentOutput[];
  process: string;
  // New field to track the current agent summary
  currentAgentSummary?: AgentSummary;
};

// Keep existing types for backward compatibility
export type AgentInput = {
  id: string;
  type: string;
  label: string;
  icon: string;
  description?: string;
};

export type AgentOutput = {
  id: string;
  type: string;
  label: string;
  icon: string;
  description?: string;
};

// Re-export new types for convenience
export type {
  AgentBuilderResponse,
  AgentSummary,
} from "./agent-builder-response";
