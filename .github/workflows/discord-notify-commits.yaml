name: Discord Notification

on:
  push:
    branches: [main, master]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Send Discord webhook
        run: |
          curl -H "Content-Type: application/json" \
            -d '{
              "embeds": [{
                "title": "New Commit Pushed",
                "description": "**Repository:** ${{ github.repository }}\n**Branch:** ${{ github.ref_name }}",
                "color": 65280,
                "author": {
                  "name": "${{ github.event.head_commit.author.name }}",
                  "icon_url": "https://github.com/${{ github.actor }}.png"
                },
                "fields": [
                  {
                    "name": "Commit Message",
                    "value": "${{ github.event.head_commit.message }}",
                    "inline": false
                  },
                  {
                    "name": "Commit Hash",
                    "value": "[`${{ github.event.head_commit.id }}`](${{ github.event.head_commit.url }})",
                    "inline": true
                  }
                ],
                "timestamp": "${{ github.event.head_commit.timestamp }}"
              }]
            }' \
            ${{ secrets.DISCORD_WEBHOOK_URL }}