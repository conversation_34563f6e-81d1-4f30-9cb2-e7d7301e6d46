"use client";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { ChatInterface } from "./ChatInterface";
import { AgentConfiguration } from "./AgentConfiguration";
import { usePanelPersistence } from "@/hooks/usePanelPersistence";

const PANEL_STORAGE_KEY = "agent-builder-panel-sizes";
const DEFAULT_CHAT_SIZE = 60;
const DEFAULT_CONFIG_SIZE = 40;

export function AgentBuilderPage() {
  const { isClient, panelSizes, handlePanelResize } = usePanelPersistence({
    storageKey: PANEL_STORAGE_KEY,
    defaultChatSize: DEFAULT_CHAT_SIZE,
    defaultConfigSize: DEFAULT_CONFIG_SIZE,
  });

  // Don't render panels until we're on the client to avoid hydration mismatch
  if (!isClient) {
    return (
      <div className="h-screen bg-background flex">
        <div className="flex-1 border-r border-border">
          <ChatInterface />
        </div>
        <div className="w-96">
          <AgentConfiguration />
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-background">
      <PanelGroup direction="horizontal" onLayout={handlePanelResize}>
        {/* Left Panel - Chat Interface */}
        <Panel defaultSize={panelSizes.chat} minSize={30}>
          <div className="h-full border-r border-border">
            <ChatInterface />
          </div>
        </Panel>

        {/* Resize Handle */}
        <PanelResizeHandle className="w-2 bg-border hover:bg-accent transition-colors duration-200 cursor-col-resize" />

        {/* Right Panel - AI Agent Configuration */}
        <Panel defaultSize={panelSizes.config} minSize={25}>
          <div className="h-full">
            <AgentConfiguration />
          </div>
        </Panel>
      </PanelGroup>
    </div>
  );
}
