"use client";

import { useCallback } from "react";
import { AgentBuilderResponse } from "@/types/agent-builder-response";
import { ChatMessage } from "@/types/agent-builder";
import { trpc } from "@/utils/trpc/client";
import { useAgentBuilderStore } from "@/stores/agentBuilderStore";

export interface UseAgentBuilderReturn {
  // Chat state
  messages: ChatMessage[];
  isLoading: boolean;

  // Actions
  sendMessage: (content: string) => Promise<void>;
  reset: () => void;
}

export function useAgentBuilder(): UseAgentBuilderReturn {
  // Get state and actions from Zustand store
  const {
    messages,
    isLoading,
    addMessage,
    setLoading,
    addAgentVersion,
    reset,
    getCurrentAgent,
  } = useAgentBuilderStore();

  // tRPC mutation for agent generation
  const generateAgentMutation = trpc.agentBuilder.generateAgent.useMutation();

  const sendMessage = useCallback(
    async (content: string) => {
      if (isLoading || !content.trim()) return;

      setLoading(true);

      // Add user message
      addMessage({
        type: "user",
        content: content.trim(),
      });

      try {
        // Prepare messages for tRPC (include the new user message)
        const allMessages = [
          ...messages,
          {
            id: `temp-${Date.now()}`,
            type: "user" as const,
            content: content.trim(),
            timestamp: new Date().toISOString(),
          },
        ];

        const apiMessages = allMessages.map((msg) => ({
          id: msg.id,
          role:
            msg.type === "user" ? ("user" as const) : ("assistant" as const),
          content: msg.content,
          timestamp: msg.timestamp,
        }));

        // Get current agent's SOP if it exists
        const currentAgent = getCurrentAgent();
        const previousSop = currentAgent?.sop;

        // Call the tRPC mutation
        const result = await generateAgentMutation.mutateAsync({
          messages: apiMessages,
          previousSop,
        });

        if (!result.success) {
          throw new Error("API returned unsuccessful response");
        }

        const structuredResponse: AgentBuilderResponse = result.data;

        // Add assistant message with structured response
        addMessage({
          type: "assistant",
          content: structuredResponse.message,
          structuredResponse,
        });

        // Add new agent version
        addAgentVersion(structuredResponse);
      } catch (error) {
        console.error("Error sending message:", error);

        // Add error message
        addMessage({
          type: "assistant",
          content:
            "I apologize, but I encountered an error while processing your request. Please try again.",
        });
      } finally {
        setLoading(false);
      }
    },
    [
      isLoading,
      messages,
      addMessage,
      setLoading,
      addAgentVersion,
      generateAgentMutation,
    ]
  );

  return {
    // Chat state
    messages,
    isLoading,

    // Actions
    sendMessage,
    reset,
  };
}
