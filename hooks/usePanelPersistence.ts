"use client";
import { useEffect, useState } from "react";

interface PanelSizes {
  chat: number;
  config: number;
}

interface UsePanelPersistenceOptions {
  storageKey: string;
  defaultChatSize: number;
  defaultConfigSize: number;
}

export function usePanelPersistence({
  storageKey,
  defaultChatSize,
  defaultConfigSize,
}: UsePanelPersistenceOptions) {
  const [isClient, setIsClient] = useState(false);
  const [panelSizes, setPanelSizes] = useState<PanelSizes>({
    chat: defaultChatSize,
    config: defaultConfigSize,
  });

  // Ensure we're on the client side before accessing localStorage
  useEffect(() => {
    setIsClient(true);

    // Load saved panel sizes from localStorage
    const savedSizes = localStorage.getItem(storageKey);
    if (savedSizes) {
      try {
        const parsed = JSON.parse(savedSizes);
        setPanelSizes(parsed);
      } catch (error) {
        console.warn("Failed to parse saved panel sizes:", error);
      }
    }
  }, [storageKey]);

  // Save panel sizes to localStorage when they change
  const handlePanelResize = (sizes: number[]) => {
    const newSizes: PanelSizes = {
      chat: sizes[0] || defaultChatSize,
      config: sizes[1] || defaultConfigSize,
    };

    setPanelSizes(newSizes);

    if (isClient) {
      localStorage.setItem(storageKey, JSON.stringify(newSizes));
    }
  };

  return {
    isClient,
    panelSizes,
    handlePanelResize,
  };
}
