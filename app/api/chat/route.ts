import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { messages } = await req.json();

  const result = streamText({
    model: openai("gpt-3.5-turbo"),
    system:
      "You are a helpful AI assistant. Keep responses concise and helpful.",
    messages,
  });

  return result.toDataStreamResponse();
}
